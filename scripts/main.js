// 抖音评论区交互功能

document.addEventListener('DOMContentLoaded', function() {
    initializeCommentInteractions();
    initializeInputArea();
    initializeScrollBehavior();
    updateTime();
});

// 初始化评论交互功能
function initializeCommentInteractions() {
    // 点赞功能
    const likeButtons = document.querySelectorAll('.like-btn');
    likeButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            toggleLike(this);
        });
    });

    // 回复功能
    const replyButtons = document.querySelectorAll('.reply-btn');
    replyButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            showReplyInput(this);
        });
    });

    // 展开回复功能
    const replyToggles = document.querySelectorAll('.replies-toggle');
    replyToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            toggleReplies(this);
        });
    });

    // 更多操作功能
    const moreButtons = document.querySelectorAll('.more-icon');
    moreButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            showMoreOptions(this);
        });
    });
}

// 点赞切换功能
function toggleLike(button) {
    const likeIcon = button.querySelector('svg');
    const likeCount = button.querySelector('.like-count');
    const isLiked = button.classList.contains('liked');

    if (isLiked) {
        // 取消点赞
        button.classList.remove('liked');
        likeIcon.style.fill = '#999';
        if (likeCount) {
            const currentCount = parseInt(likeCount.textContent);
            likeCount.textContent = currentCount - 1;
        }
    } else {
        // 点赞
        button.classList.add('liked');
        likeIcon.style.fill = '#ff2d55';
        if (likeCount) {
            const currentCount = parseInt(likeCount.textContent) || 0;
            likeCount.textContent = currentCount + 1;
        } else {
            // 如果没有点赞数，创建一个
            const countSpan = document.createElement('span');
            countSpan.className = 'like-count';
            countSpan.textContent = '1';
            button.appendChild(countSpan);
        }
    }

    // 添加点击动画
    button.style.transform = 'scale(1.2)';
    setTimeout(() => {
        button.style.transform = 'scale(1)';
    }, 150);
}

// 显示回复输入框
function showReplyInput(button) {
    const commentItem = button.closest('.comment-item');
    const username = commentItem.querySelector('.username').textContent;
    const inputArea = document.querySelector('.comment-input-area');
    const placeholder = inputArea.querySelector('.input-placeholder');
    
    // 更新输入框占位符
    placeholder.textContent = `回复 ${username}:`;
    placeholder.style.color = '#007aff';
    
    // 聚焦到输入区域
    inputArea.scrollIntoView({ behavior: 'smooth' });
    
    // 添加回复状态
    inputArea.dataset.replyTo = username;
}

// 展开/收起回复
function toggleReplies(toggle) {
    const isExpanded = toggle.classList.contains('expanded');
    const toggleText = toggle.querySelector('span');
    const toggleIcon = toggle.querySelector('svg');
    
    if (isExpanded) {
        // 收起回复
        toggle.classList.remove('expanded');
        toggleText.textContent = toggleText.textContent.replace('收起', '展开');
        toggleIcon.style.transform = 'rotate(0deg)';
        
        // 移除回复列表（如果存在）
        const repliesList = toggle.parentNode.querySelector('.replies-list');
        if (repliesList) {
            repliesList.remove();
        }
    } else {
        // 展开回复
        toggle.classList.add('expanded');
        toggleText.textContent = toggleText.textContent.replace('展开', '收起');
        toggleIcon.style.transform = 'rotate(180deg)';
        
        // 创建并显示回复列表
        createRepliesList(toggle);
    }
}

// 创建回复列表
function createRepliesList(toggle) {
    const repliesList = document.createElement('div');
    repliesList.className = 'replies-list';
    repliesList.style.marginTop = '12px';
    repliesList.style.paddingLeft = '20px';
    repliesList.style.borderLeft = '2px solid #f0f0f0';
    
    // 模拟回复数据
    const replies = [
        { username: '用户A', text: '说得对！', time: '1小时前' },
        { username: '用户B', text: '我也这么觉得', time: '30分钟前' }
    ];
    
    replies.forEach(reply => {
        const replyItem = document.createElement('div');
        replyItem.className = 'reply-item';
        replyItem.style.padding = '8px 0';
        replyItem.style.fontSize = '13px';
        replyItem.innerHTML = `
            <span style="color: #007aff; font-weight: 600;">${reply.username}</span>
            <span style="color: #000; margin-left: 8px;">${reply.text}</span>
            <span style="color: #999; margin-left: 12px; font-size: 11px;">${reply.time}</span>
        `;
        repliesList.appendChild(replyItem);
    });
    
    toggle.parentNode.appendChild(repliesList);
}

// 显示更多操作选项
function showMoreOptions(button) {
    // 创建操作菜单
    const menu = document.createElement('div');
    menu.className = 'more-options-menu';
    menu.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 12px 20px;
        border-radius: 8px;
        font-size: 14px;
        z-index: 1000;
        animation: fadeInUp 0.3s ease;
    `;
    menu.textContent = '举报';
    
    document.body.appendChild(menu);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (menu.parentNode) {
            menu.remove();
        }
    }, 3000);
    
    // 点击菜单外区域关闭
    document.addEventListener('click', function closeMenu(e) {
        if (!menu.contains(e.target)) {
            menu.remove();
            document.removeEventListener('click', closeMenu);
        }
    });
}

// 初始化输入区域
function initializeInputArea() {
    const inputContainer = document.querySelector('.input-container');
    const placeholder = document.querySelector('.input-placeholder');
    const emojiBtn = document.querySelector('.emoji-icon');
    const atBtn = document.querySelector('.at-icon');
    
    // 点击输入区域
    inputContainer.addEventListener('click', function() {
        showInputModal();
    });
    
    // 表情按钮
    emojiBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        showEmojiPanel();
    });
    
    // @按钮
    atBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        showAtPanel();
    });
}

// 显示输入模态框
function showInputModal() {
    const modal = document.createElement('div');
    modal.className = 'input-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 2000;
        display: flex;
        align-items: flex-end;
    `;
    
    const inputPanel = document.createElement('div');
    inputPanel.style.cssText = `
        width: 100%;
        background: white;
        padding: 20px;
        border-radius: 12px 12px 0 0;
        animation: slideUp 0.3s ease;
    `;
    
    inputPanel.innerHTML = `
        <div style="display: flex; gap: 12px; align-items: flex-end;">
            <textarea placeholder="说点什么..." style="
                flex: 1;
                border: 1px solid #ddd;
                border-radius: 20px;
                padding: 10px 15px;
                font-size: 16px;
                resize: none;
                outline: none;
                max-height: 100px;
            "></textarea>
            <button style="
                background: #007aff;
                color: white;
                border: none;
                border-radius: 20px;
                padding: 10px 20px;
                font-size: 16px;
                cursor: pointer;
            ">发送</button>
        </div>
    `;
    
    modal.appendChild(inputPanel);
    document.body.appendChild(modal);
    
    // 聚焦到输入框
    const textarea = inputPanel.querySelector('textarea');
    setTimeout(() => textarea.focus(), 100);
    
    // 关闭模态框
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            modal.remove();
        }
    });
    
    // 发送按钮
    const sendBtn = inputPanel.querySelector('button');
    sendBtn.addEventListener('click', function() {
        const text = textarea.value.trim();
        if (text) {
            addNewComment(text);
            modal.remove();
        }
    });
}

// 添加新评论
function addNewComment(text) {
    const commentsList = document.querySelector('.comments-list');
    const newComment = document.createElement('div');
    newComment.className = 'comment-item';
    newComment.innerHTML = `
        <div class="comment-avatar">
            <img src="images/avatars/user.jpg" alt="用户头像">
        </div>
        <div class="comment-content">
            <div class="comment-header">
                <span class="username">我</span>
            </div>
            <div class="comment-text">${text}</div>
            <div class="comment-footer">
                <span class="comment-time">刚刚</span>
                <span class="reply-btn">回复</span>
                <div class="comment-actions">
                    <div class="like-btn">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#999">
                            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    commentsList.insertBefore(newComment, commentsList.firstChild);
    
    // 重新初始化新评论的交互
    initializeCommentInteractions();
    
    // 滚动到新评论
    newComment.scrollIntoView({ behavior: 'smooth' });
}

// 初始化滚动行为
function initializeScrollBehavior() {
    const commentsList = document.querySelector('.comments-list');
    
    // 平滑滚动
    commentsList.addEventListener('scroll', function() {
        // 可以在这里添加滚动相关的逻辑
    });
}

// 更新时间显示
function updateTime() {
    const timeElement = document.querySelector('.time');
    const now = new Date();
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    timeElement.textContent = `${hours}:${minutes}`;
}

// 显示表情面板
function showEmojiPanel() {
    console.log('显示表情面板');
    // 这里可以实现表情选择功能
}

// 显示@面板
function showAtPanel() {
    console.log('显示@用户面板');
    // 这里可以实现@用户功能
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes slideUp {
        from {
            transform: translateY(100%);
        }
        to {
            transform: translateY(0);
        }
    }
`;
document.head.appendChild(style);
