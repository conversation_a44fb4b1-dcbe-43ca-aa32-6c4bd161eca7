<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>视频占位图生成器</title>
    <style>
        body { margin: 0; font-family: Arial, sans-serif; }
        .placeholder {
            width: 375px;
            height: 600px;
            background: linear-gradient(45deg, #8B4513, #D2691E, #CD853F);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
            position: relative;
            overflow: hidden;
        }
        .placeholder::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.3)"/><circle cx="80" cy="30" r="1.5" fill="rgba(255,255,255,0.2)"/><circle cx="60" cy="70" r="1" fill="rgba(255,255,255,0.4)"/><circle cx="30" cy="80" r="2.5" fill="rgba(255,255,255,0.1)"/></svg>');
            opacity: 0.3;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }
        .subtitle {
            font-size: 16px;
            text-align: center;
            opacity: 0.9;
        }
        .soldiers {
            position: absolute;
            bottom: 100px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 48px;
            opacity: 0.6;
        }
    </style>
</head>
<body>
    <div class="placeholder">
        <div class="soldiers">⚔️🏰⚔️</div>
        <div class="title">战场场景</div>
        <div class="subtitle">竟是战场改变自1912年的</div>
    </div>
    
    <script>
        // 可以右键保存这个页面的截图作为video-frame.jpg
        console.log('请右键保存此页面截图为 video-frame.jpg');
    </script>
</body>
</html>
