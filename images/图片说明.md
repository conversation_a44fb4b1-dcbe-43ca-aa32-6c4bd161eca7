# 图片资源说明

## 需要的图片文件

### 视频画面
- **文件名**: `video-frame.jpg`
- **描述**: 战争场景的视频截图，显示古代战场和士兵
- **尺寸**: 建议 375x600px 或更高分辨率
- **来源**: 可以从电影《约翰·卡特》或类似的科幻战争电影中截取

### 用户头像 (avatars目录)

#### avatar1.jpg
- **用户名**: 施云飞
- **描述**: 男性头像，可以是任意真实或卡通头像
- **尺寸**: 100x100px

#### avatar2.jpg  
- **用户名**: 张明_38636
- **描述**: 男性头像，不同于avatar1
- **尺寸**: 100x100px

#### avatar3.jpg
- **用户名**: Tom
- **描述**: 可以是外国人头像或英文名相关头像
- **尺寸**: 100x100px

#### avatar4.jpg
- **用户名**: 一颗小小葱
- **描述**: 可爱或有趣的头像，符合这个昵称的风格
- **尺寸**: 100x100px

#### avatar5.jpg
- **用户名**: 老君山网约车
- **描述**: 可以是风景头像或汽车相关头像
- **尺寸**: 100x100px

#### user.jpg
- **用户名**: 我（当前用户）
- **描述**: 默认用户头像
- **尺寸**: 100x100px

## 图片获取建议

### 免费图片网站
1. **Unsplash** (https://unsplash.com/)
   - 高质量免费图片
   - 搜索关键词: "war scene", "battle", "portrait"

2. **Pexels** (https://pexels.com/)
   - 免费商用图片
   - 搜索关键词: "people", "avatar", "portrait"

3. **Pixabay** (https://pixabay.com/)
   - 免费图片和头像
   - 有很多卡通头像可选

### 头像生成工具
1. **Avataaars Generator** (https://getavataaars.com/)
   - 生成卡通风格头像
   - 可自定义各种特征

2. **Robohash** (https://robohash.org/)
   - 根据文本生成独特头像
   - 多种风格可选

### 视频截图
- 可以从YouTube上的电影预告片或片段中截取
- 搜索"约翰·卡特 战争场景"或"古代战争电影"
- 使用截图工具保存合适的画面

## 临时解决方案

如果暂时没有图片，可以使用以下占位符：

### 在线占位图片服务
- `https://via.placeholder.com/375x600/333333/FFFFFF?text=Video`
- `https://via.placeholder.com/100x100/666666/FFFFFF?text=Avatar1`
- `https://via.placeholder.com/100x100/777777/FFFFFF?text=Avatar2`

### 本地占位符
可以创建简单的彩色方块作为临时占位符，等真实图片准备好后再替换。

## 注意事项
- 所有图片都应该是正方形（头像）或适合移动端显示的比例
- 确保图片文件大小适中，避免加载过慢
- 头像图片建议使用JPG或PNG格式
- 视频画面建议使用JPG格式以减小文件大小
