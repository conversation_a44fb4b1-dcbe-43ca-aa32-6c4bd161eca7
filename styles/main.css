/* 重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: #000;
    color: #fff;
    overflow-x: hidden;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* iPhone状态栏 */
.status-bar {
    height: 44px;
    background-color: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 20px;
    font-size: 17px;
    font-weight: 600;
    position: relative;
    z-index: 1000;
}

.status-left .time {
    color: #fff;
}

.status-right {
    display: flex;
    align-items: center;
    gap: 6px;
}

.signal-bars {
    display: flex;
    gap: 2px;
    align-items: flex-end;
}

.signal-bars .bar {
    width: 3px;
    background-color: #fff;
    border-radius: 1px;
}

.signal-bars .bar:nth-child(1) { height: 4px; }
.signal-bars .bar:nth-child(2) { height: 6px; }
.signal-bars .bar:nth-child(3) { height: 8px; }
.signal-bars .bar:nth-child(4) { height: 10px; }

.network {
    color: #fff;
    font-size: 15px;
    font-weight: 600;
}

.battery {
    display: flex;
    align-items: center;
    gap: 4px;
}

.battery-level {
    color: #fff;
    font-size: 15px;
    font-weight: 600;
}

.battery-icon {
    width: 24px;
    height: 12px;
    border: 1px solid #fff;
    border-radius: 2px;
    position: relative;
}

.battery-icon::after {
    content: '';
    position: absolute;
    right: -3px;
    top: 3px;
    width: 2px;
    height: 6px;
    background-color: #fff;
    border-radius: 0 1px 1px 0;
}

.battery-fill {
    width: 80%;
    height: 100%;
    background-color: #fff;
    border-radius: 1px;
}

/* 视频区域 */
.video-container {
    flex: 1;
    position: relative;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0; /* 移除padding，用内部元素控制 */
}

.video-content {
    width: 100%; /* 占满容器宽度 */
    height: 100%; /* 占满容器高度 */
    position: relative;
    background-color: #000;
    display: flex;
    align-items: center;
    justify-content: center;
}

.video-frame {
    width: auto; /* 自动宽度 */
    height: 100%; /* 高度占满 */
    max-width: 100%;
    object-fit: contain; /* 保持比例，可能会有黑边 */
    border-radius: 0;
}

.video-subtitle {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 16px;
    font-weight: 500;
}

.video-search-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.3);
    border-radius: 50%;
}

/* 评论区容器 */
.comments-container {
    background-color: #fff;
    border-radius: 12px 12px 0 0;
    max-height: 60vh;
    display: flex;
    flex-direction: column;
    color: #000;
}

/* 评论头部 */
.comments-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    border-bottom: 1px solid #f0f0f0;
}

.comments-count {
    font-size: 16px;
    font-weight: 600;
    color: #000;
}

.header-actions {
    display: flex;
    gap: 16px;
    align-items: center;
}

/* 评论列表 */
.comments-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 20px;
}

.comment-item {
    display: flex;
    gap: 12px;
    padding: 16px 0;
    border-bottom: 1px solid #f5f5f5;
}

.comment-item:last-child {
    border-bottom: none;
}

.comment-avatar {
    flex-shrink: 0;
}

.comment-avatar img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-content {
    flex: 1;
}

.comment-header {
    margin-bottom: 6px;
}

.username {
    font-size: 14px;
    font-weight: 600;
    color: #333;
}

.comment-text {
    font-size: 15px;
    line-height: 1.4;
    color: #000;
    margin-bottom: 8px;
}

.comment-footer {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 8px;
}

.comment-time,
.comment-location {
    font-size: 12px;
    color: #999;
}

.reply-btn {
    font-size: 12px;
    color: #999;
    cursor: pointer;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-left: auto;
}

.like-btn {
    display: flex;
    align-items: center;
    gap: 4px;
    cursor: pointer;
}

.like-count {
    font-size: 12px;
    color: #999;
}

.replies-toggle {
    display: flex;
    align-items: center;
    gap: 4px;
    font-size: 12px;
    color: #666;
    cursor: pointer;
}

/* 底部输入区 */
.comment-input-area {
    padding: 12px 20px 20px;
    border-top: 1px solid #f0f0f0;
    background-color: #fff;
}

.input-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background-color: #f8f8f8;
    border-radius: 20px;
    padding: 10px 16px;
}

.input-placeholder {
    font-size: 14px;
    color: #999;
    flex: 1;
}

.input-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* 底部指示器 */
.bottom-indicator {
    width: 134px;
    height: 5px;
    background-color: #000;
    border-radius: 3px;
    margin: 8px auto 0;
}
