/* 移动端适配样式 */

/* 确保在移动设备上的正确显示 */
@media screen and (max-width: 768px) {
    body {
        font-size: 14px;
        -webkit-text-size-adjust: 100%;
        -webkit-tap-highlight-color: transparent;
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        user-select: none;
    }

    /* 状态栏在小屏幕上的调整 */
    .status-bar {
        height: 40px;
        padding: 0 16px;
        font-size: 16px;
    }

    .signal-bars .bar {
        width: 2px;
    }

    .signal-bars .bar:nth-child(1) { height: 3px; }
    .signal-bars .bar:nth-child(2) { height: 5px; }
    .signal-bars .bar:nth-child(3) { height: 7px; }
    .signal-bars .bar:nth-child(4) { height: 9px; }

    .battery-icon {
        width: 22px;
        height: 11px;
    }

    /* 视频区域适配 */
    .video-subtitle {
        bottom: 15px;
        font-size: 15px;
        padding: 6px 12px;
    }

    .video-search-icon {
        top: 15px;
        right: 15px;
        width: 36px;
        height: 36px;
    }

    /* 评论区适配 */
    .comments-container {
        max-height: 65vh;
        border-radius: 10px 10px 0 0;
    }

    .comments-header {
        padding: 14px 16px 10px;
    }

    .comments-count {
        font-size: 15px;
    }

    .comments-list {
        padding: 0 16px;
    }

    .comment-item {
        padding: 14px 0;
        gap: 10px;
    }

    .comment-avatar img {
        width: 32px;
        height: 32px;
    }

    .username {
        font-size: 13px;
    }

    .comment-text {
        font-size: 14px;
        line-height: 1.3;
    }

    .comment-footer {
        gap: 10px;
    }

    .comment-time,
    .comment-location,
    .reply-btn {
        font-size: 11px;
    }

    .like-count {
        font-size: 11px;
    }

    .replies-toggle {
        font-size: 11px;
    }

    /* 输入区适配 */
    .comment-input-area {
        padding: 10px 16px 16px;
    }

    .input-container {
        padding: 8px 14px;
    }

    .input-placeholder {
        font-size: 13px;
    }

    .input-actions {
        gap: 10px;
    }

    /* 底部指示器 */
    .bottom-indicator {
        width: 120px;
        height: 4px;
        margin: 6px auto 0;
    }
}

/* 超小屏幕适配 */
@media screen and (max-width: 375px) {
    .status-bar {
        height: 38px;
        padding: 0 14px;
        font-size: 15px;
    }

    .comments-header {
        padding: 12px 14px 8px;
    }

    .comments-list {
        padding: 0 14px;
    }

    .comment-item {
        padding: 12px 0;
        gap: 8px;
    }

    .comment-avatar img {
        width: 30px;
        height: 30px;
    }

    .comment-input-area {
        padding: 8px 14px 14px;
    }
}

/* 触摸优化 */
.reply-btn,
.like-btn,
.replies-toggle,
.header-actions svg,
.input-actions svg {
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    transition: opacity 0.2s ease;
}

.reply-btn:active,
.like-btn:active,
.replies-toggle:active {
    opacity: 0.6;
}

/* 滚动优化 */
.comments-list {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

.comments-list::-webkit-scrollbar {
    display: none;
}

/* 防止缩放 */
input, textarea, select {
    font-size: 16px !important;
}

/* 安全区域适配（iPhone X及以上） */
@supports (padding: max(0px)) {
    .status-bar {
        padding-left: max(16px, env(safe-area-inset-left));
        padding-right: max(16px, env(safe-area-inset-right));
    }

    .comment-input-area {
        padding-bottom: max(16px, env(safe-area-inset-bottom));
    }
}

/* 横屏适配 */
@media screen and (orientation: landscape) and (max-height: 500px) {
    .comments-container {
        max-height: 70vh;
    }

    .video-subtitle {
        bottom: 10px;
        font-size: 14px;
    }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .comment-avatar img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .comments-container {
        background-color: #1a1a1a;
        color: #fff;
    }

    .comments-header {
        border-bottom-color: #333;
    }

    .comments-count {
        color: #fff;
    }

    .comment-item {
        border-bottom-color: #333;
    }

    .username {
        color: #fff;
    }

    .comment-text {
        color: #e0e0e0;
    }

    .input-container {
        background-color: #333;
    }

    .input-placeholder {
        color: #999;
    }

    .comment-input-area {
        background-color: #1a1a1a;
        border-top-color: #333;
    }
}
